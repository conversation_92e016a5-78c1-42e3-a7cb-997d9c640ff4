# Descrição da Mudança

[Descreva aqui as alterações feitas neste PR de forma clara e concisa]

## Tipo de Mudança

- [ ] Correção de Bug
- [ ] Nova Funcionalidade
- [ ] Melhoria de Performance
- [ ] Refatoração de Código
- [ ] Documentação
- [ ] Outro (especifique)

## Checklist

- [ ] O código segue as convenções de estilo do projeto
- [ ] Testes foram adicionados/atualizados para cobrir as mudanças
- [ ] Documentação foi atualizada se necessário
- [ ] O build está passando
- [ ] Não há conflitos com a branch principal

## Impacto no Compilador

- [ ] Afeta o Parser
- [ ] Afeta o Analisador Léxico
- [ ] Afeta o Compilador
- [ ] Não afeta nenhum componente

## Testes Realizados

[Descreva os testes que foram realizados para validar as mudanças]

## Screenshots (se aplicável)

[Adicione screenshots se a mudança afetar a interface]

## Observações Adicionais

[Adicione qualquer informação adicional que seja relevante] 