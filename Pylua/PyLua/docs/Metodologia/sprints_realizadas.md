# Sprints Realizadas

## Sprint 1: Preparação e Análise Léxica
**Período:** 24/03/2025 - 09/04/2025

### Tarefas Realizadas

| Tarefa | Responsável | Status | Observações |
|--------|-------------|--------|-------------|
| Definição de Escopo | [Artur Mendonça](https://github.com/ArtyMend07), [<PERSON>](https://github.com/BrzGab), [<PERSON>](https://github.com/lucasarruda9) | ✅ Concluída | Escopo definido e documentado |
| Configuração do Ambiente | [<PERSON>](https://github.com/lucasarruda9) (repositório), [<PERSON>](https://github.com/samuelalves<PERSON>) (ferramentas) | ✅ Concluída | Setup inicial do projeto |
| Pesquisa Comparativa | [Artur Mendonça](https://github.com/ArtyMend07) | ✅ Concluída | Realizada e documentada |
| Definição de Tokens | [Artur Mendonça](https://github.com/ArtyMend07) | ✅ Concluída | Tokens definidos e documentados |
| Implementação do Scanner | [Artur Mendonça](https://github.com/ArtyMend07), [Lucas Mendonça](https://github.com/lucasarruda9), [Gabriel Lopes](https://github.com/BrzGab) | ✅ Concluída | Scanner funcional implementado |

### Entregáveis
- ✅ Scanner implementado e funcional
- ✅ Tokens definidos e documentados
- ✅ Ambiente configurado

## Sprint 2: Análise Sintática
**Período:** 10/04/2025 - 28/04/2025

### Tarefas Realizadas

| Tarefa | Responsável | Status | Observações |
|--------|-------------|--------|-------------|
| Definição da Gramática | [Gabriel Lopes](https://github.com/BrzGab) | ✅ Concluída | Gramática definida e documentada |
| Implementação do Parser | [Gabriel Lopes](https://github.com/BrzGab) | ✅ Concluída | Parser implementado |
| Tratamento de Erros | [Matheus Ferreira](https://github.com/matferreira1) | ✅ Concluída | Tratamento de erros implementado |
| Integração Léxico-Sintático | [Gabriel Lopes](https://github.com/BrzGab) | ✅ Concluída | Integração realizada com sucesso |
| Preparação para P1 | [Artur Mendonça](https://github.com/ArtyMend07), [Gabriel Lopes](https://github.com/BrzGab), [Guilherme Meister](https://github.com/gmeister18), [Lucas Mendonça](https://github.com/lucasarruda9), [Matheus Ferreira](https://github.com/matferreira1), [Samuel Alves](https://github.com/samuelalvess) | ✅ Concluída | Documentação e slides preparados |
| Apresentação P1 | [Artur Mendonça](https://github.com/ArtyMend07), [Gabriel Lopes](https://github.com/BrzGab), [Guilherme Meister](https://github.com/gmeister18), [Lucas Mendonça](https://github.com/lucasarruda9), [Matheus Ferreira](https://github.com/matferreira1), [Samuel Alves](https://github.com/samuelalvess) | ✅ Concluída | Apresentação realizada |

### Entregáveis
- ✅ Parser implementado
- ✅ Gramática definida
- ✅ Apresentação P1 realizada
- ✅ Tratamento de erros implementado

## Sprint 3: Representação Interna
**Período:** 01/05/2025 - 14/05/2025

### Tarefas Realizadas

| Tarefa | Responsável | Status | Observações |
|--------|-------------|--------|-------------|
| Design da AST | [Gabriel Lopes](https://github.com/BrzGab) | ✅ Concluída | Estrutura da AST definida |
| Implementação da AST | [Lucas Mendonça](https://github.com/lucasarruda9), [Guilherme Meister](https://github.com/gmeister18) | ✅ Concluída | AST implementada com sucesso |
| Tabela de Símbolos | [Artur Mendonça](https://github.com/ArtyMend07), [Gabriel Lopes](https://github.com/BrzGab) | ✅ Concluída | Tabela de símbolos funcional |
| Visualização da AST | [Lucas Mendonça](https://github.com/lucasarruda9) | ✅ Concluída | Ferramenta de visualização implementada |
| Integração Parser-AST | [Lucas Mendonça](https://github.com/lucasarruda9) | ✅ Concluída | Integração realizada com sucesso |
| Documentação | [Artur Mendonça](https://github.com/ArtyMend07) | ✅ Concluída | Documentação atualizada |
| Testes Automatizados | [Artur Mendonça](https://github.com/ArtyMend07), [Guilherme Meister](https://github.com/gmeister18) | ✅ Concluída | Testes implementados e executados |

### Entregáveis
- ✅ AST implementada e funcional
- ✅ Tabela de símbolos implementada
- ✅ Documentação atualizada
- ✅ Testes automatizados implementados

## Status Geral do Projeto

### Componentes Implementados
- ✅ Scanner (Analisador Léxico)
- ✅ Parser (Analisador Sintático)
- ✅ Tratamento de Erros
- ✅ Integração entre Scanner e Parser
- ✅ AST (Árvore Sintática Abstrata)
- ✅ Tabela de Símbolos

### Próximos Passos
1. Iniciar implementação da análise semântica
2. Preparar para geração de código intermediário

## Observações
- A Sprint 1 foi concluída com sucesso, com a implementação do scanner funcional
- A Sprint 2 teve progresso parcial, com a implementação do parser, mas faltando a integração com o scanner
- Algumas tarefas planejadas não foram realizadas conforme o cronograma inicial
- A pesquisa comparativa precisa ser documentada formalmente

- Além das tarefas planejadas, algumas adições importantes foram realizadas fora do escopo original das sprints:
    - Documentação de Metodologia: [Artur Mendonça](https://github.com/ArtyMend07) elaborou e adicionou a seção de metodologia do projeto, detalhando processos e práticas adotadas.
    - Documentação de Ferramentas: [Samuel Alves](https://github.com/samuelalvess) criou e manteve atualizada a documentação das ferramentas utilizadas no projeto.
    - Documentação das Sprints: [Lucas Mendonça](https://github.com/lucasarruda9) e [Gabriel Lopes](https://github.com/BrzGab) organizaram e atualizaram a documentação referente ao andamento das sprints.
    - Documentação de Definição: [Guilherme Meister](https://github.com/gmeister18) elaborou o documento de definição do projeto, estabelecendo os requisitos e a base conceitual do compilador.

## Referências Bibliográficas

- Schwaber, K.; Sutherland, J. (2020). The Scrum Guide. The Definitive Guide to Scrum: The Rules of the Game. Disponível em: https://scrumguides.org/
- Rubin, K. S. (2012). Essential Scrum: A Practical Guide to the Most Popular Agile Process. Addison-Wesley.

## Histórico de Versões

| Versão | Descrição | Autor(es) | Data | Revisor(es) | Data de Revisão |
|--------|-----------|-----------|------|-------------|-----------------|
| 1.0 | Criação inicial da documentação das sprints realizadas | [Artur Mendonça](https://github.com/ArtyMend07) | 02/05/2025 | [Lucas Mendonça](https://github.com/lucasarruda9) | 02/05/2025 |
| 1.1 | Atualização das sprints realizadas | [Matheus Ferreira](https://github.com/matferreira1) | 07/05/2025 | [Lucas Mendonça](https://github.com/lucasarruda9) | 07/05/2025 |
| 1.2 | Adição da Sprint 3 e atualização de responsáveis | [Artur Mendonça](https://github.com/ArtyMend07) | 27/05/2025 | [Guilherme Meister](https://github.com/gmeister18) | 27/05/2025 |