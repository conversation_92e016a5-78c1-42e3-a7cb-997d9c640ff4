# Exemplo: Expressões complexas em Python

# Variáveis iniciais
base = 10
altura = 5
raio = 3

# Cálculos geométricos
area_retangulo = base * altura
perimetro_retangulo = 2 * (base + altura)
area_circulo = 3.14159 * raio * raio
circunferencia = 2 * 3.14159 * raio

# Expressões aninhadas
resultado1 = (base + altura) * 2
resultado2 = (area_retangulo + area_circulo) / 2
resultado3 = ((base * altura) + (raio * raio)) * 3

# Operações com precedência
calculo = base + altura * raio - 5
calculo2 = (base + altura) * (raio - 1)
calculo3 = base * altura + raio * raio
