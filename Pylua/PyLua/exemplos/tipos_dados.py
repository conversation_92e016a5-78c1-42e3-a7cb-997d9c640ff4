# Exemplo: Diferentes tipos de dados

# Números inteiros
idade = 25
ano = 2024
contador = 0

# Números deci<PERSON>is (float)
pi = 3.14159
temperatura = 36.5
preco = 19.99

# Strings
nome = "PyLua"
versao = "1.0"
mensagem = "Compilador Python para Lua"

# Booleanos
ativo = True
concluido = False
debug = True

# Operações mistas
total = idade + temperatura
descricao = nome + " " + versao
resultado_logico = ativo and not concluido
