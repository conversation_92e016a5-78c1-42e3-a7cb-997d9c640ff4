# Exemplo completo para demonstrar o gerador de código Lua

# Declarações de variáveis
nome = "PyLua"
versao = 1
pi = 3.14159
ativo = True

# Operações aritméticas
soma = 10 + 5
subtracao = 20 - 8
multiplicacao = 6 * 7
divisao = 15 / 3
modulo = 17 % 5
potencia = 2 ** 8

# Operações compostas
x = 100
x += 50
y = 200
y -= 25
z = 10
z *= 3

# Expressões complexas
resultado = (x + y) * z / 2
final = resultado + pi * 2
